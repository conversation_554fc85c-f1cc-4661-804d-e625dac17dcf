import PDFDocument from 'pdfkit';
import { fetchResponsesForExport, createExportSuccessResponse, createExportErrorResponse, ExportOptions } from './exportResponsesBase';

export const exportResponsesAsPdf = async (surveyId: string, accountId: string, includeDeleted: boolean = false, whereConditions?: any) => {
  try {
    const options: ExportOptions = {
      surveyId,
      accountId,
      includeDeleted,
      whereConditions,
    };

    const fetchResult = await fetchResponsesForExport(options);

    if (!fetchResult.success) {
      return createExportErrorResponse('pdf', fetchResult.data);
    }

    const responses = fetchResult.data;

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    const chunks: Buffer[] = [];

    // Collect PDF data
    doc.on('data', chunk => chunks.push(chunk));

    return new Promise(resolve => {
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        resolve(createExportSuccessResponse(surveyId, 'pdf', pdfBuffer));
      });

      // Add title
      doc.fontSize(20).text('Survey Responses Export', { align: 'center' });
      doc.moveDown();

      // Add metadata
      doc
        .fontSize(12)
        .text(`Survey ID: ${surveyId}`)
        .text(`Total Responses: ${responses.length}`)
        .text(`Exported At: ${new Date().toISOString()}`)
        .text(`Include Deleted: ${includeDeleted ? 'Yes' : 'No'}`);

      doc.moveDown();

      // Add responses
      responses.forEach((response, index) => {
        // Check if we need a new page
        if (doc.y > 700) {
          doc.addPage();
        }

        doc.fontSize(14).text(`Response #${index + 1}`, { underline: true });
        doc.moveDown(0.5);

        doc
          .fontSize(10)
          .text(`Created: ${response.created_at}`)
          .text(`Deleted: ${response.is_deleted ? 'Yes' : 'No'}`);

        if (response.is_deleted && response.delete_reason) {
          doc.text(`Delete Reason: ${response.delete_reason}`);
        }

        // Add response data
        if (response.response_data) {
          doc.moveDown(0.5);
          doc.fontSize(12).text('Response Data:', { underline: true });
          doc.fontSize(10).text(JSON.stringify(response.response_data, null, 2));
        }

        // Add respondent details
        if (response.respondent_details) {
          doc.moveDown(0.5);
          doc.fontSize(12).text('Respondent Details:', { underline: true });
          doc.fontSize(10).text(JSON.stringify(response.respondent_details, null, 2));
        }

        // Add respondent info
        if (response.respondent_info) {
          doc.moveDown(0.5);
          doc.fontSize(12).text('Respondent Info:', { underline: true });
          doc.fontSize(10).text(JSON.stringify(response.respondent_info, null, 2));
        }

        // Add meta information
        if (response.meta) {
          doc.moveDown(0.5);
          doc.fontSize(12).text('Meta Information:', { underline: true });
          doc.fontSize(10).text(JSON.stringify(response.meta, null, 2));
        }

        doc.moveDown();
        doc.strokeColor('#cccccc').lineWidth(1).moveTo(50, doc.y).lineTo(550, doc.y).stroke();
        doc.moveDown();
      });

      // Finalize the PDF
      doc.end();
    });
  } catch (error) {
    return createExportErrorResponse('pdf', error);
  }
};
